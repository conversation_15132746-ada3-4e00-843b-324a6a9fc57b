[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "datax"
version = "0.1.0"
description = "A comprehensive data migration toolkit for ETL/ELT operations"
readme = "README.md"
license = { text = "MIT" }
keywords = [
    "datax",
    "etl",
    "elt",
    "prefect",
    "database",
    "s3",
    "loader",
    "extractor",
    "transformer"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Topic :: Database",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: System :: Archiving",
]
requires-python = ">=3.13"
dependencies = [
    "prefect>=3.0.0",
    "pandas",
    "pyarrow",
    "pyyaml",
    "pydantic",
    "sqlalchemy",
    "asyncmy",
    "asyncpg",
    "aioch",
    "aioboto3",
    "motor",
    "minio",
]

[project.optional-dependencies]
dev = [
    "ruff",
    "mypy",
    "pyright",
    "pytest",
    "pytest-cov",
    "pytest-mock",
    "pytest-asyncio",
    "black",
    "isort",
    "flake8",
    "pre-commit",
    "bandit",
    "safety"
]
docs = [
    "mkdocs",
    "mkdocs-material",
    "mkdocstrings[python]",
    "mkdocs-gen-files",
    "mkdocs-literate-nav",
]
test = [
    "pytest",
    "pytest-cov",
    "pytest-mock",
    "pytest-asyncio",
    "moto[s3]",
    "testcontainers",
    "testcontainers[mysql]",
    "testcontainers[postgres]",
    "testcontainers[mongodb]",
    "testcontainers[clickhouse]",
]
all = [
    "datax[dev,docs,test]",
]

[project.urls]
Homepage = "https://github.com/nikewong/jingwei"
Documentation = "https://datax.readthedocs.io"
Repository = "https://github.com/nikewong/jingwei.git"
Issues = "https://github.com/nikewong/jingwei/issues"
Changelog = "https://github.com/nikewong/jingwei/blob/main/CHANGELOG.md"

[project.scripts]
datax = "datax.cli:main"
apply-s3-lifecycle = "scripts.apply_s3_lifecycle:main"

[tool.hatch.build.targets.wheel]
packages = ["src/datax"]

[tool.hatch.build.targets.sdist]
include = [
    "src/",
    "tests/",
    "docs/",
    "scripts/",
    "configs/",
    "README.md",
    "LICENSE",
    "CHANGELOG.md",
]

[tool.black]
line-length = 88
target-version = ['py313']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["datax"]
known_third_party = ["prefect", "pydantic", "sqlalchemy", "pandas", "boto3"]

[tool.mypy]
python_version = "3.13"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "prefect.*",
    "sqlalchemy.*",
    "pandas.*",
    "boto3.*",
    "botocore.*",
    "psycopg2.*",
    "pymysql.*",
    "pyodbc.*",
    "redis.*",
    "pyarrow.*",
    "psutil.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
asyncio_mode = "auto"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "database: marks tests that require database connection",
    "s3: marks tests that require S3 connection",
    "redis: marks tests that require Redis connection",
    "asyncio: marks tests as async tests",
    "postgres: marks tests that require PostgreSQL container",
    "mysql: marks tests that require MySQL container",
    "clickhouse: marks tests that require ClickHouse container",
    "mongodb: marks tests that require MongoDB container",
    "minio: marks tests that require MinIO container",
]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]

[tool.coverage.run]
source = ["src/datax"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/.venv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
skip_covered = false

[tool.bandit]
exclude_dirs = ["tests", "venv", ".venv"]
skips = ["B101", "B601"]

[tool.ruff]
target-version = "py313"
line-length = 88
exclude = [".venv", "build", "dist", "docs", "typings"]

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"]
"tests/**/*" = ["B018", "S101", "S105", "S106"]

[tool.ruff.lint.isort]
known-first-party = ["datax"]
known-third-party = ["prefect", "pydantic", "sqlalchemy", "pandas", "boto3"]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
docstring-code-format = true
